// 卡牌类
export class Card {
  constructor(suit, rank) {
    this.suit = suit // 花色: 'spades', 'hearts', 'diamonds', 'clubs', 'joker'
    this.rank = rank // 点数: 3-10, 'J', 'Q', 'K', 'A', '2', 'small_joker', 'big_joker'
    this.id = this.generateId()
    this.value = this.calculateValue()
  }

  generateId() {
    if (this.suit === 'joker') {
      return this.rank === 'small_joker' ? 'joker_small' : 'joker_big'
    }
    return `${this.suit}_${this.rank}`
  }

  calculateValue() {
    // 计算牌的大小值，用于比较
    const rankValues = {
      '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9, '10': 10,
      'J': 11, 'Q': 12, 'K': 13, 'A': 14, '2': 15,
      'small_joker': 16, 'big_joker': 17
    }
    return rankValues[this.rank] || 0
  }

  getDisplayName() {
    if (this.suit === 'joker') {
      return this.rank === 'small_joker' ? '小王' : '大王'
    }
    
    const suitNames = {
      'spades': '♠', 'hearts': '♥', 'diamonds': '♦', 'clubs': '♣'
    }
    
    const rankNames = {
      'J': 'J', 'Q': 'Q', 'K': 'K', 'A': 'A'
    }
    
    const displayRank = rankNames[this.rank] || this.rank
    return `${suitNames[this.suit]}${displayRank}`
  }

  getColor() {
    if (this.suit === 'joker') {
      return this.rank === 'small_joker' ? '#000000' : '#ff0000'
    }
    return ['hearts', 'diamonds'].includes(this.suit) ? '#ff0000' : '#000000'
  }
}

// 牌堆类
export class Deck {
  constructor() {
    this.cards = []
    this.initializeDeck()
  }

  initializeDeck() {
    const suits = ['spades', 'hearts', 'diamonds', 'clubs']
    const ranks = ['3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A', '2']
    
    // 添加普通牌
    suits.forEach(suit => {
      ranks.forEach(rank => {
        this.cards.push(new Card(suit, rank))
      })
    })
    
    // 添加大小王
    this.cards.push(new Card('joker', 'small_joker'))
    this.cards.push(new Card('joker', 'big_joker'))
  }

  shuffle() {
    for (let i = this.cards.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [this.cards[i], this.cards[j]] = [this.cards[j], this.cards[i]]
    }
  }

  deal(count) {
    return this.cards.splice(0, count)
  }

  getRemaining() {
    return this.cards.slice()
  }
}

// 玩家手牌类
export class PlayerHand {
  constructor() {
    this.cards = []
  }

  addCards(cards) {
    this.cards.push(...cards)
    this.sortCards()
  }

  removeCards(cards) {
    cards.forEach(card => {
      const index = this.cards.findIndex(c => c.id === card.id)
      if (index !== -1) {
        this.cards.splice(index, 1)
      }
    })
  }

  sortCards() {
    this.cards.sort((a, b) => a.value - b.value)
  }

  getCards() {
    return this.cards.slice()
  }

  getCardCount() {
    return this.cards.length
  }

  hasCard(cardId) {
    return this.cards.some(card => card.id === cardId)
  }
}
