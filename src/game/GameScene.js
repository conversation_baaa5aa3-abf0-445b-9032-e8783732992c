import Phaser from 'phaser'
import { GameState } from './GameState.js'

// 游戏场景类
class GameMainScene extends Phaser.Scene {
  constructor() {
    super({ key: 'GameMainScene' })
    this.gameState = new GameState()
    this.cardSprites = new Map() // 存储卡牌精灵
    this.playerHandAreas = {} // 玩家手牌区域
    this.uiElements = {} // UI元素
  }

  preload() {
    // 预加载资源
    console.log('GameMainScene: 预加载资源')
    // 这里可以加载卡牌图片等资源
  }

  create() {
    // 游戏初始化逻辑
    console.log('GameMainScene: 创建游戏场景')

    // 设置背景色
    this.cameras.main.setBackgroundColor('#0f5132')

    // 添加标题
    this.uiElements.title = this.add.text(400, 30, '斗地主游戏', {
      fontSize: '20px',
      color: '#ffffff',
      fontFamily: 'Arial'
    }).setOrigin(0.5)

    // 添加游戏桌面
    this.createGameTable()

    // 添加玩家位置
    this.createPlayerPositions()

    // 创建UI界面
    this.createGameUI()

    // 开始游戏
    this.startNewGame()
  }

  createGameTable() {
    // 创建游戏桌面
    const centerX = 400
    const centerY = 300

    // 桌面背景
    const table = this.add.graphics()
    table.fillStyle(0x2d5a27)
    table.fillRoundedRect(centerX - 200, centerY - 150, 400, 300, 20)

    // 桌面边框
    table.lineStyle(4, 0x1a3d1a)
    table.strokeRoundedRect(centerX - 200, centerY - 150, 400, 300, 20)
  }

  createPlayerPositions() {
    // 创建三个玩家位置
    const positions = [
      { x: 400, y: 580, name: '玩家1 (我)', position: 'bottom' },
      { x: 120, y: 180, name: '玩家2', position: 'left' },
      { x: 680, y: 180, name: '玩家3', position: 'right' }
    ]

    positions.forEach((pos) => {
      // 玩家头像区域
      const avatar = this.add.graphics()
      avatar.fillStyle(0x4a90e2)
      avatar.fillCircle(pos.x, pos.y, 25)

      // 玩家名称
      this.add.text(pos.x, pos.y + 40, pos.name, {
        fontSize: '14px',
        color: '#ffffff',
        fontFamily: 'Arial'
      }).setOrigin(0.5)

      // 手牌数量显示
      this.uiElements[`${pos.position}CardCount`] = this.add.text(pos.x, pos.y + 55, '17张', {
        fontSize: '12px',
        color: '#ffff00',
        fontFamily: 'Arial'
      }).setOrigin(0.5)

      // 设置手牌区域
      this.playerHandAreas[pos.position] = {
        x: pos.x,
        y: pos.y,
        cards: []
      }
    })
  }

  createGameUI() {
    // 创建游戏状态显示
    this.uiElements.gamePhase = this.add.text(400, 10, '等待开始...', {
      fontSize: '14px',
      color: '#ffff00',
      fontFamily: 'Arial'
    }).setOrigin(0.5)

    // 创建操作按钮区域
    this.createActionButtons()
  }

  createActionButtons() {
    const buttonY = 540 // 在手牌和玩家头像之间

    // 开始游戏按钮
    this.uiElements.startButton = this.createButton(400, buttonY, '开始游戏', () => this.startNewGame())

    // 叫地主按钮
    this.uiElements.bidLandlordButton = this.createButton(350, buttonY, '叫地主', () => this.bidLandlord())
    this.uiElements.bidLandlordButton.setVisible(false)

    // 不叫按钮
    this.uiElements.passButton = this.createButton(450, buttonY, '不叫', () => this.passBid())
    this.uiElements.passButton.setVisible(false)
  }

  createButton(x, y, text, callback) {
    // 创建按钮背景
    const buttonBg = this.add.graphics()
    buttonBg.fillStyle(0x4a90e2)
    buttonBg.fillRoundedRect(x - 40, y - 15, 80, 30, 5)
    buttonBg.lineStyle(2, 0x2c5aa0)
    buttonBg.strokeRoundedRect(x - 40, y - 15, 80, 30, 5)

    // 创建按钮文字
    const buttonText = this.add.text(x, y, text, {
      fontSize: '16px',
      color: '#ffffff',
      fontFamily: 'Arial'
    }).setOrigin(0.5)

    // 创建按钮容器
    const button = this.add.container(0, 0, [buttonBg, buttonText])
    button.setSize(80, 30)
    button.setInteractive()

    // 添加点击效果
    button.on('pointerdown', () => {
      buttonBg.clear()
      buttonBg.fillStyle(0x2c5aa0)
      buttonBg.fillRoundedRect(x - 40, y - 15, 80, 30, 5)
      callback()

      // 恢复按钮样式
      setTimeout(() => {
        buttonBg.clear()
        buttonBg.fillStyle(0x4a90e2)
        buttonBg.fillRoundedRect(x - 40, y - 15, 80, 30, 5)
        buttonBg.lineStyle(2, 0x2c5aa0)
        buttonBg.strokeRoundedRect(x - 40, y - 15, 80, 30, 5)
      }, 100)
    })

    return button
  }

  startNewGame() {
    console.log('开始新游戏')
    this.gameState.startGame()
    this.updateUI()
    this.displayPlayerCards()
  }

  updateUI() {
    const gameInfo = this.gameState.getGameInfo()

    // 更新游戏阶段显示
    const phaseTexts = {
      'waiting': '等待开始...',
      'dealing': '发牌中...',
      'bidding': '叫牌阶段',
      'playing': '游戏进行中',
      'finished': '游戏结束'
    }
    this.uiElements.gamePhase.setText(phaseTexts[gameInfo.phase] || gameInfo.phase)

    // 更新玩家手牌数量
    gameInfo.players.forEach(player => {
      const countText = this.uiElements[`${player.position}CardCount`]
      if (countText) {
        countText.setText(`${player.cardCount}张`)
        if (player.isLandlord) {
          countText.setColor('#ff0000')
        }
      }
    })

    // 更新按钮显示
    this.updateActionButtons(gameInfo)
  }

  updateActionButtons(gameInfo) {
    // 隐藏所有按钮
    if (this.uiElements.startButton) this.uiElements.startButton.setVisible(false)
    if (this.uiElements.bidLandlordButton) this.uiElements.bidLandlordButton.setVisible(false)
    if (this.uiElements.passButton) this.uiElements.passButton.setVisible(false)

    if (gameInfo.phase === 'waiting') {
      if (this.uiElements.startButton) this.uiElements.startButton.setVisible(true)
    } else if (gameInfo.phase === 'bidding' && gameInfo.currentPlayer === 0) {
      // 轮到玩家1叫牌
      if (this.uiElements.bidLandlordButton) this.uiElements.bidLandlordButton.setVisible(true)
      if (this.uiElements.passButton) this.uiElements.passButton.setVisible(true)
    }
  }

  displayPlayerCards() {
    // 显示玩家1（底部）的手牌
    const player1 = this.gameState.getPlayer(1)
    if (player1) {
      this.displayBottomPlayerCards(player1.hand.getCards())
    }
  }

  displayBottomPlayerCards(cards) {
    // 清除之前的卡牌
    this.playerHandAreas.bottom.cards.forEach(cardSprite => {
      cardSprite.destroy()
    })
    this.playerHandAreas.bottom.cards = []

    // 显示新的手牌
    const totalCards = cards.length
    const cardSpacing = Math.min(25, 500 / totalCards) // 动态调整间距
    const startX = 400 - (totalCards - 1) * cardSpacing / 2 // 居中显示
    const y = 480 // 手牌位置

    cards.forEach((card, index) => {
      const x = startX + index * cardSpacing
      const cardSprite = this.createCardSprite(x, y, card)
      this.playerHandAreas.bottom.cards.push(cardSprite)
    })
  }

  createCardSprite(x, y, card) {
    // 创建卡牌背景
    const cardBg = this.add.graphics()
    cardBg.fillStyle(0xffffff)
    cardBg.fillRoundedRect(x - 20, y - 30, 40, 60, 5)
    cardBg.lineStyle(2, 0x000000)
    cardBg.strokeRoundedRect(x - 20, y - 30, 40, 60, 5)

    // 添加卡牌文字
    const cardText = this.add.text(x, y, card.getDisplayName(), {
      fontSize: '12px',
      color: card.getColor(),
      fontFamily: 'Arial'
    }).setOrigin(0.5)

    // 创建卡牌容器
    const cardContainer = this.add.container(0, 0, [cardBg, cardText])
    cardContainer.setSize(40, 60)
    cardContainer.setInteractive()
    cardContainer.cardData = card

    // 添加点击事件
    cardContainer.on('pointerdown', () => {
      this.selectCard(cardContainer)
    })

    return cardContainer
  }

  selectCard(cardContainer) {
    // 卡牌选择逻辑
    console.log('选择卡牌:', cardContainer.cardData.getDisplayName())

    // 切换选中状态
    if (cardContainer.selected) {
      cardContainer.y = 480 // 恢复到原位置
      cardContainer.selected = false
    } else {
      cardContainer.y = 460 // 向上移动表示选中
      cardContainer.selected = true
    }
  }

  bidLandlord() {
    console.log('叫地主')
    this.gameState.bid(1, 'landlord')
    this.updateUI()

    // 模拟其他玩家的叫牌
    setTimeout(() => {
      this.simulateAIBidding()
    }, 1000)
  }

  passBid() {
    console.log('不叫')
    this.gameState.bid(1, 'pass')
    this.updateUI()

    // 模拟其他玩家的叫牌
    setTimeout(() => {
      this.simulateAIBidding()
    }, 1000)
  }

  simulateAIBidding() {
    const gameInfo = this.gameState.getGameInfo()
    if (gameInfo.phase !== 'bidding') return

    const currentPlayer = gameInfo.currentPlayer
    if (currentPlayer === 0) return // 轮到玩家1，不需要模拟

    // 简单的AI逻辑：随机决定叫牌或不叫
    const shouldBid = Math.random() > 0.7 // 30%概率叫地主
    const bidType = shouldBid ? 'landlord' : 'pass'

    console.log(`AI玩家${currentPlayer + 1} ${bidType === 'landlord' ? '叫地主' : '不叫'}`)
    this.gameState.bid(currentPlayer + 1, bidType)
    this.updateUI()

    // 如果还在叫牌阶段，继续模拟
    if (this.gameState.phase === 'bidding') {
      setTimeout(() => {
        this.simulateAIBidding()
      }, 1000)
    }
  }
}

// 游戏管理器类
export default class GameScene {
  constructor(container) {
    this.container = container
    this.game = null
    this.init()
  }

  init() {
    try {
      console.log('GameScene: 初始化游戏')

      const config = {
        type: Phaser.AUTO,
        width: 800,
        height: 600,
        parent: this.container,
        backgroundColor: '#0f5132',
        scene: [GameMainScene],
        scale: {
          mode: Phaser.Scale.FIT,
          autoCenter: Phaser.Scale.CENTER_BOTH
        }
      }

      this.game = new Phaser.Game(config)
      console.log('GameScene: 游戏初始化成功')
    } catch (error) {
      console.error('GameScene: 游戏初始化失败', error)
      throw error
    }
  }

  destroy() {
    if (this.game) {
      console.log('GameScene: 销毁游戏实例')
      this.game.destroy(true)
      this.game = null
    }
  }
}
