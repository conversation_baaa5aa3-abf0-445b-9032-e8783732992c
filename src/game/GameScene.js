import Phaser from 'phaser'
import { GameState } from './GameState.js'

// 游戏场景类
class GameMainScene extends Phaser.Scene {
  constructor() {
    super({ key: 'GameMainScene' })
    this.gameState = new GameState()
    this.cardSprites = new Map() // 存储卡牌精灵
    this.playerHandAreas = {} // 玩家手牌区域
    this.uiElements = {} // UI元素
  }

  preload() {
    // 预加载资源
    console.log('GameMainScene: 预加载资源')

    // 加载背景图片
    this.load.image('tableBg1', 'images/table_bg_1.jpg')
    this.load.image('tableBg2', 'images/table_bg_2.jpg')
    this.load.image('bg2', 'images/bg2.png')
    this.load.image('creatRoomBg', 'images/creatroom_bg.png')
    this.load.image('joinBk', 'images/join_bk.png')

    // 加载头像框图片
    this.load.image('avatarFrame', 'images/room_touxiang.png')

    // 加载头像图片
    this.load.image('avatar1', 'images/headimage/avatar_1.png')
    this.load.image('avatar2', 'images/headimage/avatar_2.png')
    this.load.image('avatar3', 'images/headimage/avatar_3.png')
    this.load.image('avatar4', 'images/headimage/avatar_4.png')

    // 加载按钮图片
    this.load.image('btnStart', 'images/bt_start_0.png')
    this.load.image('btnReady', 'images/btnready.png')
    this.load.image('btnBujiao', 'images/button/btn_bujiao.png')
    this.load.image('btnQiangzhuang', 'images/button/qiangzhuang.png')
    this.load.image('btnBuqiangzhuang', 'images/button/buqiangzhuang.png')

    // 加载叫牌相关图片
    this.load.image('join0', 'images/join_0.png')
    this.load.image('join1', 'images/join_1.png')
    this.load.image('join2', 'images/join_2.png')
    this.load.image('join3', 'images/join_3.png')
  }

  create() {
    // 游戏初始化逻辑
    console.log('GameMainScene: 创建游戏场景')

    // 添加背景图片 - 使用更合适的斗地主背景
    const bg = this.add.image(400, 300, 'bg2')
    bg.setDisplaySize(800, 600) // 缩放到适合屏幕大小

    // 添加标题
    this.uiElements.title = this.add.text(400, 30, '斗地主游戏', {
      fontSize: '20px',
      color: '#ffffff',
      fontFamily: 'Arial',
      stroke: '#000000',
      strokeThickness: 2
    }).setOrigin(0.5)

    // 添加玩家位置
    this.createPlayerPositions()

    // 创建UI界面
    this.createGameUI()

    // 开始游戏
    this.startNewGame()
  }



  createPlayerPositions() {
    // 标准斗地主布局 - 确保在600px高度内
    const positions = [
      { x: 150, y: 520, name: '玩家1 (我)', position: 'bottom', avatar: 'avatar1' },
      { x: 80, y: 150, name: '玩家2', position: 'left', avatar: 'avatar2' },
      { x: 720, y: 150, name: '玩家3', position: 'right', avatar: 'avatar3' }
    ]

    positions.forEach((pos) => {
      // 玩家头像框
      const avatarFrame = this.add.image(pos.x, pos.y, 'avatarFrame')
      avatarFrame.setDisplaySize(60, 60)
      avatarFrame.setOrigin(0.5)

      // 玩家头像图片（在框内）
      const avatar = this.add.image(pos.x, pos.y, pos.avatar)
      avatar.setDisplaySize(45, 45) // 稍小一些，适合框内
      avatar.setOrigin(0.5)

      // 玩家名称 - 对于底部玩家，名称显示在头像上方
      const nameY = pos.position === 'bottom' ? pos.y - 40 : pos.y + 40
      this.add.text(pos.x, nameY, pos.name, {
        fontSize: '12px',
        color: '#ffffff',
        fontFamily: 'Arial',
        stroke: '#000000',
        strokeThickness: 1
      }).setOrigin(0.5)

      // 手牌数量显示
      const countY = pos.position === 'bottom' ? pos.y - 25 : pos.y + 55
      this.uiElements[`${pos.position}CardCount`] = this.add.text(pos.x, countY, '17张', {
        fontSize: '10px',
        color: '#ffff00',
        fontFamily: 'Arial',
        stroke: '#000000',
        strokeThickness: 1
      }).setOrigin(0.5)

      // 设置手牌区域
      this.playerHandAreas[pos.position] = {
        x: pos.x,
        y: pos.y,
        cards: []
      }
    })
  }

  createGameUI() {
    // 创建游戏状态显示
    this.uiElements.gamePhase = this.add.text(400, 10, '等待开始...', {
      fontSize: '14px',
      color: '#ffff00',
      fontFamily: 'Arial'
    }).setOrigin(0.5)

    // 创建操作按钮区域
    this.createActionButtons()
  }

  createActionButtons() {
    // 按钮放在右下角区域，确保在屏幕内
    const buttonY = 520
    const rightX = 600

    // 开始游戏按钮 - 使用图片按钮
    this.uiElements.startButton = this.createImageButton(rightX, buttonY, 'btnStart', () => this.startNewGame())

    // 叫地主按钮 - 使用图片按钮
    this.uiElements.bidLandlordButton = this.createImageButton(rightX - 80, buttonY, 'btnQiangzhuang', () => this.bidLandlord())
    this.uiElements.bidLandlordButton.setVisible(false)

    // 不叫按钮 - 使用图片按钮
    this.uiElements.passButton = this.createImageButton(rightX + 80, buttonY, 'btnBujiao', () => this.passBid())
    this.uiElements.passButton.setVisible(false)
  }

  createImageButton(x, y, imageKey, callback) {
    // 创建图片按钮
    const button = this.add.image(x, y, imageKey)
    button.setInteractive()
    button.setOrigin(0.5)

    // 添加点击效果
    button.on('pointerdown', () => {
      button.setTint(0x888888) // 变暗效果
      callback()

      // 恢复按钮样式
      setTimeout(() => {
        button.clearTint()
      }, 100)
    })

    // 添加悬停效果
    button.on('pointerover', () => {
      button.setTint(0xcccccc)
    })

    button.on('pointerout', () => {
      button.clearTint()
    })

    return button
  }

  createTextButton(x, y, text, callback) {
    // 创建按钮背景
    const buttonBg = this.add.graphics()
    buttonBg.fillStyle(0x4a90e2)
    buttonBg.fillRoundedRect(x - 40, y - 15, 80, 30, 5)
    buttonBg.lineStyle(2, 0x2c5aa0)
    buttonBg.strokeRoundedRect(x - 40, y - 15, 80, 30, 5)

    // 创建按钮文字
    const buttonText = this.add.text(x, y, text, {
      fontSize: '16px',
      color: '#ffffff',
      fontFamily: 'Arial'
    }).setOrigin(0.5)

    // 创建按钮容器
    const button = this.add.container(0, 0, [buttonBg, buttonText])
    button.setSize(80, 30)
    button.setInteractive()

    // 添加点击效果
    button.on('pointerdown', () => {
      buttonBg.clear()
      buttonBg.fillStyle(0x2c5aa0)
      buttonBg.fillRoundedRect(x - 40, y - 15, 80, 30, 5)
      callback()

      // 恢复按钮样式
      setTimeout(() => {
        buttonBg.clear()
        buttonBg.fillStyle(0x4a90e2)
        buttonBg.fillRoundedRect(x - 40, y - 15, 80, 30, 5)
        buttonBg.lineStyle(2, 0x2c5aa0)
        buttonBg.strokeRoundedRect(x - 40, y - 15, 80, 30, 5)
      }, 100)
    })

    return button
  }

  startNewGame() {
    console.log('开始新游戏')
    this.gameState.startGame()
    this.updateUI()
    this.displayPlayerCards()
  }

  updateUI() {
    const gameInfo = this.gameState.getGameInfo()

    // 更新游戏阶段显示
    const phaseTexts = {
      'waiting': '等待开始...',
      'dealing': '发牌中...',
      'bidding': '叫牌阶段',
      'playing': '游戏进行中',
      'finished': '游戏结束'
    }
    this.uiElements.gamePhase.setText(phaseTexts[gameInfo.phase] || gameInfo.phase)

    // 更新玩家手牌数量
    gameInfo.players.forEach(player => {
      const countText = this.uiElements[`${player.position}CardCount`]
      if (countText) {
        countText.setText(`${player.cardCount}张`)
        if (player.isLandlord) {
          countText.setColor('#ff0000')
        }
      }
    })

    // 更新按钮显示
    this.updateActionButtons(gameInfo)
  }

  updateActionButtons(gameInfo) {
    // 隐藏所有按钮
    if (this.uiElements.startButton) this.uiElements.startButton.setVisible(false)
    if (this.uiElements.bidLandlordButton) this.uiElements.bidLandlordButton.setVisible(false)
    if (this.uiElements.passButton) this.uiElements.passButton.setVisible(false)

    if (gameInfo.phase === 'waiting') {
      if (this.uiElements.startButton) this.uiElements.startButton.setVisible(true)
    } else if (gameInfo.phase === 'bidding' && gameInfo.currentPlayer === 0) {
      // 轮到玩家1叫牌
      if (this.uiElements.bidLandlordButton) this.uiElements.bidLandlordButton.setVisible(true)
      if (this.uiElements.passButton) this.uiElements.passButton.setVisible(true)
    }
  }

  displayPlayerCards() {
    // 显示玩家1（底部）的手牌
    const player1 = this.gameState.getPlayer(1)
    if (player1) {
      this.displayBottomPlayerCards(player1.hand.getCards())
    }
  }

  displayBottomPlayerCards(cards) {
    // 清除之前的卡牌
    this.playerHandAreas.bottom.cards.forEach(cardSprite => {
      cardSprite.destroy()
    })
    this.playerHandAreas.bottom.cards = []

    // 显示新的手牌
    const totalCards = cards.length
    const cardSpacing = Math.min(22, 480 / totalCards) // 动态调整间距
    const startX = 400 - (totalCards - 1) * cardSpacing / 2 // 居中显示
    const y = 340 // 手牌位置，避免与玩家信息重叠

    cards.forEach((card, index) => {
      const x = startX + index * cardSpacing
      const cardSprite = this.createCardSprite(x, y, card)
      this.playerHandAreas.bottom.cards.push(cardSprite)
    })
  }

  createCardSprite(x, y, card) {
    // 创建卡牌背景
    const cardBg = this.add.graphics()
    cardBg.fillStyle(0xffffff)
    cardBg.fillRoundedRect(x - 20, y - 30, 40, 60, 5)
    cardBg.lineStyle(2, 0x000000)
    cardBg.strokeRoundedRect(x - 20, y - 30, 40, 60, 5)

    // 添加卡牌文字
    const cardText = this.add.text(x, y, card.getDisplayName(), {
      fontSize: '12px',
      color: card.getColor(),
      fontFamily: 'Arial'
    }).setOrigin(0.5)

    // 创建卡牌容器
    const cardContainer = this.add.container(0, 0, [cardBg, cardText])
    cardContainer.setSize(40, 60)
    cardContainer.setInteractive()
    cardContainer.cardData = card

    // 添加点击事件
    cardContainer.on('pointerdown', () => {
      this.selectCard(cardContainer)
    })

    return cardContainer
  }

  selectCard(cardContainer) {
    // 卡牌选择逻辑
    console.log('选择卡牌:', cardContainer.cardData.getDisplayName())

    // 切换选中状态
    if (cardContainer.selected) {
      cardContainer.y = 340 // 恢复到原位置
      cardContainer.selected = false
    } else {
      cardContainer.y = 320 // 向上移动表示选中
      cardContainer.selected = true
    }
  }

  bidLandlord() {
    console.log('叫地主')
    this.gameState.bid(1, 'landlord')
    this.updateUI()

    // 模拟其他玩家的叫牌
    setTimeout(() => {
      this.simulateAIBidding()
    }, 1000)
  }

  passBid() {
    console.log('不叫')
    this.gameState.bid(1, 'pass')
    this.updateUI()

    // 模拟其他玩家的叫牌
    setTimeout(() => {
      this.simulateAIBidding()
    }, 1000)
  }

  simulateAIBidding() {
    const gameInfo = this.gameState.getGameInfo()
    if (gameInfo.phase !== 'bidding') return

    const currentPlayer = gameInfo.currentPlayer
    if (currentPlayer === 0) return // 轮到玩家1，不需要模拟

    // 简单的AI逻辑：随机决定叫牌或不叫
    const shouldBid = Math.random() > 0.7 // 30%概率叫地主
    const bidType = shouldBid ? 'landlord' : 'pass'

    console.log(`AI玩家${currentPlayer + 1} ${bidType === 'landlord' ? '叫地主' : '不叫'}`)
    this.gameState.bid(currentPlayer + 1, bidType)
    this.updateUI()

    // 如果还在叫牌阶段，继续模拟
    if (this.gameState.phase === 'bidding') {
      setTimeout(() => {
        this.simulateAIBidding()
      }, 1000)
    }
  }
}

// 游戏管理器类
export default class GameScene {
  constructor(container) {
    this.container = container
    this.game = null
    this.init()
  }

  init() {
    try {
      console.log('GameScene: 初始化游戏')

      const config = {
        type: Phaser.AUTO,
        width: 800,
        height: 600,
        parent: this.container,
        backgroundColor: '#0f5132',
        scene: [GameMainScene],
        scale: {
          mode: Phaser.Scale.FIT,
          autoCenter: Phaser.Scale.CENTER_BOTH
        }
      }

      this.game = new Phaser.Game(config)
      console.log('GameScene: 游戏初始化成功')
    } catch (error) {
      console.error('GameScene: 游戏初始化失败', error)
      throw error
    }
  }

  destroy() {
    if (this.game) {
      console.log('GameScene: 销毁游戏实例')
      this.game.destroy(true)
      this.game = null
    }
  }
}
