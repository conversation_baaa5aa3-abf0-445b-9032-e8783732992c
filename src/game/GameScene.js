import Phaser from 'phaser'

// 游戏场景类
class GameMainScene extends Phaser.Scene {
  constructor() {
    super({ key: 'GameMainScene' })
  }

  preload() {
    // 预加载资源
    console.log('GameMainScene: 预加载资源')
  }

  create() {
    // 游戏初始化逻辑
    console.log('GameMainScene: 创建游戏场景')

    // 设置背景色
    this.cameras.main.setBackgroundColor('#0f5132')

    // 添加标题
    this.add.text(400, 100, '斗地主游戏', {
      fontSize: '32px',
      color: '#ffffff',
      fontFamily: 'Arial'
    }).setOrigin(0.5)

    // 添加游戏桌面
    this.createGameTable()

    // 添加玩家位置
    this.createPlayerPositions()
  }

  createGameTable() {
    // 创建游戏桌面
    const centerX = 400
    const centerY = 300

    // 桌面背景
    const table = this.add.graphics()
    table.fillStyle(0x2d5a27)
    table.fillRoundedRect(centerX - 200, centerY - 150, 400, 300, 20)

    // 桌面边框
    table.lineStyle(4, 0x1a3d1a)
    table.strokeRoundedRect(centerX - 200, centerY - 150, 400, 300, 20)
  }

  createPlayerPositions() {
    // 创建三个玩家位置
    const positions = [
      { x: 400, y: 500, name: '玩家1 (我)' },
      { x: 150, y: 200, name: '玩家2' },
      { x: 650, y: 200, name: '玩家3' }
    ]

    positions.forEach((pos) => {
      // 玩家头像区域
      const avatar = this.add.graphics()
      avatar.fillStyle(0x4a90e2)
      avatar.fillCircle(pos.x, pos.y, 30)

      // 玩家名称
      this.add.text(pos.x, pos.y + 50, pos.name, {
        fontSize: '16px',
        color: '#ffffff',
        fontFamily: 'Arial'
      }).setOrigin(0.5)
    })
  }
}

// 游戏管理器类
export default class GameScene {
  constructor(container) {
    this.container = container
    this.game = null
    this.init()
  }

  init() {
    try {
      console.log('GameScene: 初始化游戏')

      const config = {
        type: Phaser.AUTO,
        width: 800,
        height: 600,
        parent: this.container,
        backgroundColor: '#0f5132',
        scene: [GameMainScene],
        scale: {
          mode: Phaser.Scale.FIT,
          autoCenter: Phaser.Scale.CENTER_BOTH
        }
      }

      this.game = new Phaser.Game(config)
      console.log('GameScene: 游戏初始化成功')
    } catch (error) {
      console.error('GameScene: 游戏初始化失败', error)
      throw error
    }
  }

  destroy() {
    if (this.game) {
      console.log('GameScene: 销毁游戏实例')
      this.game.destroy(true)
      this.game = null
    }
  }
}
