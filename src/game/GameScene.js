import Phaser from 'phaser'
import { GameState } from './GameState.js'

// 游戏场景类
class GameMainScene extends Phaser.Scene {
  constructor() {
    super({ key: 'GameMainScene' })
    this.gameState = new GameState()
    this.cardSprites = new Map() // 存储卡牌精灵
    this.playerHandAreas = {} // 玩家手牌区域
    this.uiElements = {} // UI元素
  }

  preload() {
    // 预加载资源
    console.log('GameMainScene: 预加载资源')
    // 使用标准斗地主界面设计，不需要加载外部图片
  }

  create() {
    // 游戏初始化逻辑
    console.log('GameMainScene: 创建游戏场景')

    // 创建标准斗地主游戏背景
    this.createStandardBackground()

    // 添加标题
    this.uiElements.title = this.add.text(400, 30, '斗地主', {
      fontSize: '28px',
      color: '#FFD700',
      fontFamily: 'Arial Black, Arial',
      stroke: '#8B4513',
      strokeThickness: 3,
      shadow: {
        offsetX: 2,
        offsetY: 2,
        color: '#000000',
        blur: 4,
        fill: true
      }
    }).setOrigin(0.5)

    // 创建游戏桌面
    this.createGameTable()

    // 添加玩家位置
    this.createPlayerPositions()

    // 创建UI界面
    this.createGameUI()

    // 开始游戏
    this.startNewGame()
  }



  createStandardBackground() {
    // 创建标准斗地主游戏背景 - 深绿色渐变
    const bg = this.add.graphics()

    // 主背景 - 深绿色渐变
    bg.fillGradientStyle(0x0d4f2c, 0x0d4f2c, 0x1a5f3a, 0x1a5f3a, 1)
    bg.fillRect(0, 0, 800, 600)

    // 添加纹理效果
    for (let i = 0; i < 50; i++) {
      const x = Math.random() * 800
      const y = Math.random() * 600
      const size = Math.random() * 3 + 1
      bg.fillStyle(0x2d7a4f, 0.1)
      bg.fillCircle(x, y, size)
    }
  }

  createGameTable() {
    // 创建标准斗地主游戏桌面
    const centerX = 400
    const centerY = 300
    const tableWidth = 500
    const tableHeight = 300

    // 桌面阴影
    const shadow = this.add.graphics()
    shadow.fillStyle(0x000000, 0.3)
    shadow.fillEllipse(centerX + 5, centerY + 5, tableWidth, tableHeight)

    // 桌面主体 - 椭圆形绿色桌面
    const table = this.add.graphics()
    table.fillGradientStyle(0x2d5a27, 0x2d5a27, 0x1a3d1a, 0x1a3d1a, 1)
    table.fillEllipse(centerX, centerY, tableWidth, tableHeight)

    // 桌面边框
    table.lineStyle(4, 0x8B4513, 1)
    table.strokeEllipse(centerX, centerY, tableWidth, tableHeight)

    // 内边框装饰
    table.lineStyle(2, 0xDAA520, 0.8)
    table.strokeEllipse(centerX, centerY, tableWidth - 20, tableHeight - 20)

    // 桌面中央装饰
    const centerDecor = this.add.graphics()
    centerDecor.fillStyle(0x8B4513, 0.3)
    centerDecor.fillEllipse(centerX, centerY, 100, 60)
    centerDecor.lineStyle(2, 0xDAA520, 0.6)
    centerDecor.strokeEllipse(centerX, centerY, 100, 60)
  }

  createPlayerPositions() {
    // 标准斗地主布局 - 确保在600px高度内
    const positions = [
      { x: 150, y: 520, name: '我', position: 'bottom', color: '#FFD700' },
      { x: 80, y: 150, name: '玩家2', position: 'left', color: '#87CEEB' },
      { x: 720, y: 150, name: '玩家3', position: 'right', color: '#FFA07A' }
    ]

    positions.forEach((pos, index) => {
      // 创建标准斗地主头像区域
      this.createPlayerAvatar(pos, index + 1)
    })
  }

  createPlayerAvatar(pos, playerNum) {
    // 头像背景圆形
    const avatarBg = this.add.graphics()
    avatarBg.fillStyle(0x8B4513, 1)
    avatarBg.fillCircle(pos.x, pos.y, 35)

    // 头像边框
    avatarBg.lineStyle(3, 0xDAA520, 1)
    avatarBg.strokeCircle(pos.x, pos.y, 35)

    // 内圈装饰
    avatarBg.lineStyle(2, pos.color === '#FFD700' ? 0xFF6347 : 0xFFFFFF, 0.8)
    avatarBg.strokeCircle(pos.x, pos.y, 28)

    // 头像内容 - 使用文字代替图片
    const avatarText = this.add.text(pos.x, pos.y, playerNum.toString(), {
      fontSize: '24px',
      color: '#FFFFFF',
      fontFamily: 'Arial Black, Arial',
      stroke: '#000000',
      strokeThickness: 2
    }).setOrigin(0.5)

    // 玩家名称
    const nameY = pos.position === 'bottom' ? pos.y - 50 : pos.y + 50
    const nameText = this.add.text(pos.x, nameY, pos.name, {
      fontSize: '14px',
      color: pos.color,
      fontFamily: 'Arial Black, Arial',
      stroke: '#000000',
      strokeThickness: 2
    }).setOrigin(0.5)

    // 手牌数量显示背景
    const countY = pos.position === 'bottom' ? pos.y - 30 : pos.y + 70
    const countBg = this.add.graphics()
    countBg.fillStyle(0x000000, 0.7)
    countBg.fillRoundedRect(pos.x - 25, countY - 10, 50, 20, 10)
    countBg.lineStyle(1, 0xFFD700, 1)
    countBg.strokeRoundedRect(pos.x - 25, countY - 10, 50, 20, 10)

    // 手牌数量文字
    this.uiElements[`${pos.position}CardCount`] = this.add.text(pos.x, countY, '17张', {
      fontSize: '12px',
      color: '#FFD700',
      fontFamily: 'Arial',
      stroke: '#000000',
      strokeThickness: 1
    }).setOrigin(0.5)

    // 设置手牌区域
    this.playerHandAreas[pos.position] = {
      x: pos.x,
      y: pos.y,
      cards: []
    }
  }

  createGameUI() {
    // 创建游戏状态显示
    this.uiElements.gamePhase = this.add.text(400, 10, '等待开始...', {
      fontSize: '14px',
      color: '#ffff00',
      fontFamily: 'Arial'
    }).setOrigin(0.5)

    // 创建操作按钮区域
    this.createActionButtons()
  }

  createActionButtons() {
    // 按钮放在右下角区域，确保在屏幕内
    const buttonY = 520
    const rightX = 600

    // 开始游戏按钮 - 使用标准斗地主按钮设计
    this.uiElements.startButton = this.createStandardButton(rightX, buttonY, '开始游戏', '#32CD32', () => this.startNewGame())

    // 叫地主按钮 - 使用标准斗地主按钮设计
    this.uiElements.bidLandlordButton = this.createStandardButton(rightX - 100, buttonY, '叫地主', '#FF6347', () => this.bidLandlord())
    this.uiElements.bidLandlordButton.setVisible(false)

    // 不叫按钮 - 使用标准斗地主按钮设计
    this.uiElements.passButton = this.createStandardButton(rightX + 100, buttonY, '不叫', '#87CEEB', () => this.passBid())
    this.uiElements.passButton.setVisible(false)
  }

  createStandardButton(x, y, text, color, callback) {
    // 创建标准斗地主按钮
    const buttonWidth = 90
    const buttonHeight = 35

    // 按钮阴影
    const shadow = this.add.graphics()
    shadow.fillStyle(0x000000, 0.3)
    shadow.fillRoundedRect(x - buttonWidth/2 + 2, y - buttonHeight/2 + 2, buttonWidth, buttonHeight, 8)

    // 按钮背景
    const buttonBg = this.add.graphics()
    buttonBg.fillGradientStyle(
      Phaser.Display.Color.HexStringToColor(color).color,
      Phaser.Display.Color.HexStringToColor(color).color,
      Phaser.Display.Color.HexStringToColor(color).darken(30).color,
      Phaser.Display.Color.HexStringToColor(color).darken(30).color,
      1
    )
    buttonBg.fillRoundedRect(x - buttonWidth/2, y - buttonHeight/2, buttonWidth, buttonHeight, 8)

    // 按钮边框
    buttonBg.lineStyle(2, 0xFFD700, 1)
    buttonBg.strokeRoundedRect(x - buttonWidth/2, y - buttonHeight/2, buttonWidth, buttonHeight, 8)

    // 按钮文字
    const buttonText = this.add.text(x, y, text, {
      fontSize: '16px',
      color: '#FFFFFF',
      fontFamily: 'Arial Black, Arial',
      stroke: '#000000',
      strokeThickness: 2
    }).setOrigin(0.5)

    // 创建按钮容器
    const button = this.add.container(0, 0, [shadow, buttonBg, buttonText])
    button.setSize(buttonWidth, buttonHeight)
    button.setInteractive()

    // 添加点击效果
    button.on('pointerdown', () => {
      button.setScale(0.95)
      callback()

      // 恢复按钮样式
      setTimeout(() => {
        button.setScale(1)
      }, 100)
    })

    // 添加悬停效果
    button.on('pointerover', () => {
      button.setScale(1.05)
    })

    button.on('pointerout', () => {
      button.setScale(1)
    })

    return button
  }

  startNewGame() {
    console.log('开始新游戏')
    this.gameState.startGame()
    this.updateUI()
    this.displayPlayerCards()
  }

  updateUI() {
    const gameInfo = this.gameState.getGameInfo()

    // 更新游戏阶段显示
    const phaseTexts = {
      'waiting': '等待开始...',
      'dealing': '发牌中...',
      'bidding': '叫牌阶段',
      'playing': '游戏进行中',
      'finished': '游戏结束'
    }
    this.uiElements.gamePhase.setText(phaseTexts[gameInfo.phase] || gameInfo.phase)

    // 更新玩家手牌数量
    gameInfo.players.forEach(player => {
      const countText = this.uiElements[`${player.position}CardCount`]
      if (countText) {
        countText.setText(`${player.cardCount}张`)
        if (player.isLandlord) {
          countText.setColor('#ff0000')
        }
      }
    })

    // 更新按钮显示
    this.updateActionButtons(gameInfo)
  }

  updateActionButtons(gameInfo) {
    // 隐藏所有按钮
    if (this.uiElements.startButton) this.uiElements.startButton.setVisible(false)
    if (this.uiElements.bidLandlordButton) this.uiElements.bidLandlordButton.setVisible(false)
    if (this.uiElements.passButton) this.uiElements.passButton.setVisible(false)

    if (gameInfo.phase === 'waiting') {
      if (this.uiElements.startButton) this.uiElements.startButton.setVisible(true)
    } else if (gameInfo.phase === 'bidding' && gameInfo.currentPlayer === 0) {
      // 轮到玩家1叫牌
      if (this.uiElements.bidLandlordButton) this.uiElements.bidLandlordButton.setVisible(true)
      if (this.uiElements.passButton) this.uiElements.passButton.setVisible(true)
    }
  }

  displayPlayerCards() {
    // 显示玩家1（底部）的手牌
    const player1 = this.gameState.getPlayer(1)
    if (player1) {
      this.displayBottomPlayerCards(player1.hand.getCards())
    }
  }

  displayBottomPlayerCards(cards) {
    // 清除之前的卡牌
    this.playerHandAreas.bottom.cards.forEach(cardSprite => {
      cardSprite.destroy()
    })
    this.playerHandAreas.bottom.cards = []

    // 显示新的手牌
    const totalCards = cards.length
    const cardSpacing = Math.min(22, 480 / totalCards) // 动态调整间距
    const startX = 400 - (totalCards - 1) * cardSpacing / 2 // 居中显示
    const y = 340 // 手牌位置，避免与玩家信息重叠

    cards.forEach((card, index) => {
      const x = startX + index * cardSpacing
      const cardSprite = this.createCardSprite(x, y, card)
      this.playerHandAreas.bottom.cards.push(cardSprite)
    })
  }

  createCardSprite(x, y, card) {
    // 创建标准斗地主卡牌
    const cardWidth = 45
    const cardHeight = 65

    // 卡牌阴影
    const shadow = this.add.graphics()
    shadow.fillStyle(0x000000, 0.3)
    shadow.fillRoundedRect(x - cardWidth/2 + 2, y - cardHeight/2 + 2, cardWidth, cardHeight, 6)

    // 卡牌背景
    const cardBg = this.add.graphics()
    cardBg.fillStyle(0xFFFFFF, 1)
    cardBg.fillRoundedRect(x - cardWidth/2, y - cardHeight/2, cardWidth, cardHeight, 6)

    // 卡牌边框
    cardBg.lineStyle(2, 0x333333, 1)
    cardBg.strokeRoundedRect(x - cardWidth/2, y - cardHeight/2, cardWidth, cardHeight, 6)

    // 卡牌花色和数字
    const isRed = card.suit === 'hearts' || card.suit === 'diamonds'
    const cardColor = isRed ? '#FF0000' : '#000000'

    // 主要数字/字母
    const mainText = this.add.text(x, y - 8, card.getDisplayName(), {
      fontSize: '16px',
      color: cardColor,
      fontFamily: 'Arial Black, Arial',
      fontWeight: 'bold'
    }).setOrigin(0.5)

    // 花色符号
    const suitSymbol = this.getSuitSymbol(card.suit)
    const suitText = this.add.text(x, y + 12, suitSymbol, {
      fontSize: '14px',
      color: cardColor,
      fontFamily: 'Arial'
    }).setOrigin(0.5)

    // 创建卡牌容器
    const cardContainer = this.add.container(0, 0, [shadow, cardBg, mainText, suitText])
    cardContainer.setSize(cardWidth, cardHeight)
    cardContainer.setInteractive()
    cardContainer.cardData = card

    // 添加点击事件
    cardContainer.on('pointerdown', () => {
      this.selectCard(cardContainer)
    })

    // 添加悬停效果
    cardContainer.on('pointerover', () => {
      cardContainer.setScale(1.05)
    })

    cardContainer.on('pointerout', () => {
      if (!cardContainer.selected) {
        cardContainer.setScale(1)
      }
    })

    return cardContainer
  }

  getSuitSymbol(suit) {
    const symbols = {
      'hearts': '♥',
      'diamonds': '♦',
      'clubs': '♣',
      'spades': '♠'
    }
    return symbols[suit] || '?'
  }

  selectCard(cardContainer) {
    // 卡牌选择逻辑
    console.log('选择卡牌:', cardContainer.cardData.getDisplayName())

    // 切换选中状态
    if (cardContainer.selected) {
      cardContainer.y = 340 // 恢复到原位置
      cardContainer.selected = false
    } else {
      cardContainer.y = 320 // 向上移动表示选中
      cardContainer.selected = true
    }
  }

  bidLandlord() {
    console.log('叫地主')
    this.gameState.bid(1, 'landlord')
    this.updateUI()

    // 模拟其他玩家的叫牌
    setTimeout(() => {
      this.simulateAIBidding()
    }, 1000)
  }

  passBid() {
    console.log('不叫')
    this.gameState.bid(1, 'pass')
    this.updateUI()

    // 模拟其他玩家的叫牌
    setTimeout(() => {
      this.simulateAIBidding()
    }, 1000)
  }

  simulateAIBidding() {
    const gameInfo = this.gameState.getGameInfo()
    if (gameInfo.phase !== 'bidding') return

    const currentPlayer = gameInfo.currentPlayer
    if (currentPlayer === 0) return // 轮到玩家1，不需要模拟

    // 简单的AI逻辑：随机决定叫牌或不叫
    const shouldBid = Math.random() > 0.7 // 30%概率叫地主
    const bidType = shouldBid ? 'landlord' : 'pass'

    console.log(`AI玩家${currentPlayer + 1} ${bidType === 'landlord' ? '叫地主' : '不叫'}`)
    this.gameState.bid(currentPlayer + 1, bidType)
    this.updateUI()

    // 如果还在叫牌阶段，继续模拟
    if (this.gameState.phase === 'bidding') {
      setTimeout(() => {
        this.simulateAIBidding()
      }, 1000)
    }
  }
}

// 游戏管理器类
export default class GameScene {
  constructor(container) {
    this.container = container
    this.game = null
    this.init()
  }

  init() {
    try {
      console.log('GameScene: 初始化游戏')

      const config = {
        type: Phaser.AUTO,
        width: 800,
        height: 600,
        parent: this.container,
        backgroundColor: '#0f5132',
        scene: [GameMainScene],
        scale: {
          mode: Phaser.Scale.FIT,
          autoCenter: Phaser.Scale.CENTER_BOTH
        }
      }

      this.game = new Phaser.Game(config)
      console.log('GameScene: 游戏初始化成功')
    } catch (error) {
      console.error('GameScene: 游戏初始化失败', error)
      throw error
    }
  }

  destroy() {
    if (this.game) {
      console.log('GameScene: 销毁游戏实例')
      this.game.destroy(true)
      this.game = null
    }
  }
}
