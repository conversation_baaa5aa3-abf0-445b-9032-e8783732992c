import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { message, Spin, Empty, Button } from 'antd'
import { ReloadOutlined } from '@ant-design/icons'
import { observer } from 'mobx-react-lite'
import { gameStore } from '../../store/gameStore'
import { GameConfigService, SignupService } from '../../services/gameService'
import GameCard from '../../components/GameCard'
import { GAME_STATUS } from '../../utils/constants'
import './style.css'

const GameHall = observer(() => {
  const navigate = useNavigate()
  const [loading, setLoading] = useState(false)
  const [gameConfigs, setGameConfigs] = useState([])
  const [userSignupStatuses, setUserSignupStatuses] = useState({})
  const [refreshing, setRefreshing] = useState(false)

  useEffect(() => {
    loadGameConfigs()
    // 设置定时刷新
    const interval = setInterval(loadGameConfigs, 30000) // 30秒刷新一次
    return () => clearInterval(interval)
  }, [])

  const loadGameConfigs = async () => {
    if (!refreshing) setLoading(true)
    try {
      const response = await GameConfigService.getConfigList()
      if (response.code === 0) {
        const configs = response.result.map(config => ({
          ...config,
          status: GameConfigService.calculateGameStatus(config)
        }))
        setGameConfigs(configs)

        // 加载用户报名状态
        await loadUserSignupStatuses(configs)
      }
    } catch (error) {
      console.error('加载比赛配置失败:', error)
      message.error('加载比赛信息失败')
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  const loadUserSignupStatuses = async (configs) => {
    if (!gameStore.user) return

    const statuses = {}
    for (const config of configs) {
      try {
        const status = await SignupService.checkUserSignupStatus(
          gameStore.user.id,
          config.gc_id
        )
        statuses[config.gc_id] = status
      } catch (error) {
        console.error(`检查比赛${config.gc_id}报名状态失败:`, error)
        statuses[config.gc_id] = {
          isSignedUp: false,
          isSignedIn: false
        }
      }
    }
    setUserSignupStatuses(statuses)
  }

  // 处理报名
  const handleSignUp = async (gcid) => {
    if (!gameStore.user) {
      message.error('请先登录')
      return
    }

    try {
      const response = await SignupService.signUp(gameStore.user.id, gcid)
      if (response.code === 0) {
        message.success('报名成功！')
        // 更新本地状态
        setUserSignupStatuses(prev => ({
          ...prev,
          [gcid]: { ...prev[gcid], isSignedUp: true }
        }))

        // 检查比赛状态，决定下一步操作
        const gameConfig = gameConfigs.find(config => config.gc_id === gcid)
        if (gameConfig) {
          // 如果比赛已经到了签到或等待阶段，提示用户下一步操作
          if (gameConfig.status === GAME_STATUS.SIGNIN) {
            message.info('比赛已开始签到，请及时签到！', 3)
          } else if (gameConfig.status === GAME_STATUS.WAITING) {
            message.info('比赛即将开始，正在跳转到等待页面...', 2)
            setTimeout(() => {
              gameStore.setCurrentGame(gameConfig)
              navigate(`/waiting/${gcid}`)
            }, 2000)
          }
        }

        // 刷新比赛列表
        loadGameConfigs()
      } else {
        message.error(response.msg || '报名失败')
      }
    } catch (error) {
      console.error('报名失败:', error)
      message.error('报名失败，请重试')
    }
  }

  // 处理取消报名
  const handleCancelSignUp = async (gcid) => {
    try {
      const response = await SignupService.cancelSignUp(gameStore.user.id, gcid)
      if (response.code === 0) {
        message.success('取消报名成功！')
        // 更新本地状态
        setUserSignupStatuses(prev => ({
          ...prev,
          [gcid]: { ...prev[gcid], isSignedUp: false, isSignedIn: false }
        }))
        // 刷新比赛列表
        loadGameConfigs()
      } else {
        message.error(response.msg || '取消报名失败')
      }
    } catch (error) {
      console.error('取消报名失败:', error)
      message.error('取消报名失败，请重试')
    }
  }

  // 处理签到
  const handleSignIn = async (gcid) => {
    try {
      const response = await SignupService.signIn(gameStore.user.id, gcid)
      if (response.code === 0) {
        message.success('签到成功！')
        // 更新本地状态
        setUserSignupStatuses(prev => ({
          ...prev,
          [gcid]: { ...prev[gcid], isSignedIn: true }
        }))

        // 签到成功后，检查比赛状态
        const gameConfig = gameConfigs.find(config => config.gc_id === gcid)
        if (gameConfig) {
          if (gameConfig.status === GAME_STATUS.WAITING) {
            message.info('正在跳转到等待页面...', 2)
            setTimeout(() => {
              gameStore.setCurrentGame(gameConfig)
              navigate(`/waiting/${gcid}`)
            }, 2000)
          } else {
            message.info('签到成功，请等待比赛开始！', 3)
          }
        }
      } else {
        message.error(response.msg || '签到失败')
      }
    } catch (error) {
      console.error('签到失败:', error)
      message.error('签到失败，请重试')
    }
  }

  // 处理进入比赛
  const handleEnterGame = async (gcid) => {
    const gameConfig = gameConfigs.find(config => config.gc_id === gcid)
    if (gameConfig) {
      gameStore.setCurrentGame(gameConfig)
      navigate(`/waiting/${gcid}`)
    }
  }

  // 手动刷新
  const handleRefresh = () => {
    setRefreshing(true)
    loadGameConfigs()
  }

  // 过滤比赛列表
  const getFilteredGames = () => {
    return gameConfigs.filter(config => {
      // 只显示可以报名、签到、等待或进行中的比赛
      return [GAME_STATUS.SIGNUP, GAME_STATUS.SIGNIN, GAME_STATUS.WAITING, GAME_STATUS.PLAYING].includes(config.status)
    })
  }

  const filteredGames = getFilteredGames()

  if (loading) {
    return (
      <div className="hall-loading">
        <Spin size="large" />
        <p>加载比赛信息中...</p>
      </div>
    )
  }

  return (
    <div className="game-hall">
      <div className="hall-header">
        <div className="header-content">
          <div className="title-section">
            <h1>比赛大厅</h1>
            <p>选择您要参加的比赛</p>
          </div>
          <div className="action-section">
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              loading={refreshing}
            >
              刷新
            </Button>
          </div>
        </div>
      </div>

      <div className="game-list">
        {filteredGames.length === 0 ? (
          <Empty
            description="暂无可参加的比赛"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        ) : (
          <div className="game-grid">
            {filteredGames.map(config => (
              <GameCard
                key={config.gc_id}
                gameConfig={config}
                userSignupStatus={userSignupStatuses[config.gc_id]}
                onSignUp={handleSignUp}
                onCancelSignUp={handleCancelSignUp}
                onSignIn={handleSignIn}
                onEnterGame={handleEnterGame}
                loading={loading}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  )
})

export default GameHall
