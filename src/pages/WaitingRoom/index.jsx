import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { Card, Button, Progress, message, Spin } from 'antd'
import { ClockCircleOutlined, UserOutlined } from '@ant-design/icons'
import { observer } from 'mobx-react-lite'
import { gameStore } from '../../store/gameStore'

const WaitingRoom = observer(() => {
  const { gameId } = useParams()
  const navigate = useNavigate()
  const [countdown, setCountdown] = useState(null) // 动态计算倒计时
  const [playerCount, setPlayerCount] = useState(12)
  const [totalPlayers, setTotalPlayers] = useState(18)
  const [gameConfig, setGameConfig] = useState(null)
  const [loading, setLoading] = useState(true)

  // 初始化页面数据
  useEffect(() => {
    initializeWaitingRoom()
  }, [gameId])

  // 倒计时效果
  useEffect(() => {
    if (countdown === null) return

    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          // 倒计时结束，进入游戏
          navigate(`/game/${gameId}/1`)
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [countdown, gameId, navigate])

  // 初始化等待房间
  const initializeWaitingRoom = async () => {
    setLoading(true)
    try {
      // 从gameStore获取当前比赛信息
      const currentGame = gameStore.currentGame
      if (currentGame) {
        setGameConfig(currentGame)
        setTotalPlayers(currentGame.gc_maxplayers)

        // 计算到比赛开始的倒计时
        const gameStartTime = new Date(currentGame.gc_startime)
        const now = new Date()
        const timeDiff = Math.max(0, Math.floor((gameStartTime - now) / 1000))
        setCountdown(timeDiff)

        // 模拟当前报名人数（实际应该从API获取）
        setPlayerCount(currentGame.signupCount || 12)
      }
    } catch (error) {
      console.error('初始化等待房间失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const progress = (playerCount / totalPlayers) * 100

  // 加载状态
  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '80vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      }}>
        <Spin size="large" />
      </div>
    )
  }

  return (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      minHeight: '80vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    }}>
      <Card style={{
        width: 400,
        textAlign: 'center',
        borderRadius: 16,
        boxShadow: '0 8px 32px rgba(0,0,0,0.1)'
      }}>
        <div style={{ marginBottom: 24 }}>
          <img
            src="/resources/logo_dengdai.png"
            alt="等待中"
            style={{ width: 80, height: 80, marginBottom: 16 }}
          />
          <h2>{gameConfig?.gc_name || '等待比赛开始'}</h2>
        </div>

        <div style={{ marginBottom: 24 }}>
          <div style={{
            fontSize: 48,
            fontWeight: 'bold',
            color: countdown > 60 ? '#1890ff' : '#ff4d4f',
            marginBottom: 8
          }}>
            {countdown !== null ? formatTime(countdown) : '--:--'}
          </div>
          <div style={{ color: '#666' }}>
            <ClockCircleOutlined /> 距离比赛开始还有
          </div>
        </div>

        <div style={{ marginBottom: 24 }}>
          <Progress 
            percent={progress} 
            strokeColor="#52c41a"
            format={() => `${playerCount}/${totalPlayers}`}
          />
          <div style={{ marginTop: 8, color: '#666' }}>
            <UserOutlined /> 已入场玩家
          </div>
        </div>

        <div style={{
          background: '#f5f5f5',
          padding: 16,
          borderRadius: 8,
          marginBottom: 16
        }}>
          <div style={{ marginBottom: 12 }}>
            <h4 style={{ margin: 0, fontSize: 16, color: '#333' }}>比赛信息</h4>
          </div>
          <div style={{ textAlign: 'left', fontSize: 14, color: '#666' }}>
            <p style={{ margin: '4px 0' }}>
              <strong>比赛规模：</strong>{gameConfig?.gc_rounds || 2}轮 × {gameConfig?.gc_gamesperround || 3}局
            </p>
            <p style={{ margin: '4px 0' }}>
              <strong>最少人数：</strong>{gameConfig?.gc_minplayers || 6}人开赛
            </p>
            <p style={{ margin: '4px 0' }}>
              <strong>比赛规则：</strong>{gameConfig?.gc_rules || '标准斗地主规则'}
            </p>
          </div>
        </div>

        <div style={{
          background: '#e6f7ff',
          padding: 12,
          borderRadius: 8,
          marginBottom: 16,
          border: '1px solid #91d5ff'
        }}>
          <p style={{ margin: 0, fontSize: 14, color: '#1890ff' }}>
            💡 比赛即将开始，请保持网络连接稳定
          </p>
        </div>

        <div style={{ display: 'flex', gap: 8 }}>
          <Button
            type="default"
            onClick={() => navigate('/hall')}
            style={{ flex: 1 }}
          >
            返回大厅
          </Button>
          <Button
            type="primary"
            disabled={countdown > 10}
            onClick={() => navigate(`/game/${gameId}/1`)}
            style={{ flex: 1 }}
          >
            {countdown > 10 ? '等待中...' : '进入游戏'}
          </Button>
        </div>
      </Card>
    </div>
  )
})

export default WaitingRoom
