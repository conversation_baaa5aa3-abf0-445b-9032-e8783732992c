import api from '../api'
import { GAME_STATUS } from '../utils/constants'

// 游戏配置服务
export class GameConfigService {
  // 获取最新的比赛配置
  static async getLatestConfig() {
    try {
      const response = await api.getGameConfigLast()
      return response
    } catch (error) {
      console.error('获取最新比赛配置失败:', error)
      // 返回模拟数据
      return {
        code: 0,
        result: {
          gc_id: 1,
          gc_name: '春季斗地主大赛',
          gc_time: new Date().toISOString(),
          gc_bmstartime: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          gc_bmendtime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          gc_qdstartime: new Date(Date.now() + 25 * 60 * 60 * 1000).toISOString(),
          gc_qdendtime: new Date(Date.now() + 26 * 60 * 60 * 1000).toISOString(),
          gc_startime: new Date(Date.now() + 27 * 60 * 60 * 1000).toISOString()
        }
      }
    }
  }

  // 获取比赛配置列表
  static async getConfigList(params = {}) {
    try {
      const response = await api.getGameConfigList(params)
      return response
    } catch (error) {
      console.error('获取比赛配置列表失败:', error)
      // 返回模拟数据
      return {
        code: 0,
        result: GameConfigService.getMockGameConfigs(),
        count: 3
      }
    }
  }

  // 获取模拟比赛数据
  static getMockGameConfigs() {
    const now = new Date()
    const oneHour = 60 * 60 * 1000
    const oneDay = 24 * oneHour

    return [
      {
        gc_id: 1,
        gc_name: '春季斗地主大赛',
        gc_time: new Date(now.getTime() - oneDay).toISOString(),
        gc_bmstartime: new Date(now.getTime() - oneDay).toISOString(),
        gc_bmendtime: new Date(now.getTime() + oneDay).toISOString(),
        gc_qdstartime: new Date(now.getTime() + oneDay + oneHour).toISOString(),
        gc_qdendtime: new Date(now.getTime() + oneDay + 2 * oneHour).toISOString(),
        gc_startime: new Date(now.getTime() + oneDay + 3 * oneHour).toISOString(),
        gc_maxplayers: 60,
        gc_minplayers: 15,
        gc_rounds: 5,
        gc_gamesperround: 6,
        gc_allowrepeat: 'forbid',
        gc_basescore: 100,
        gc_rules: '标准斗地主规则，三炸封顶',
        signupCount: 32,
        status: GAME_STATUS.SIGNUP
      },
      {
        gc_id: 2,
        gc_name: '夏季公开赛',
        gc_time: new Date(now.getTime() - 2 * oneDay).toISOString(),
        gc_bmstartime: new Date(now.getTime() + 2 * oneDay).toISOString(),
        gc_bmendtime: new Date(now.getTime() + 5 * oneDay).toISOString(),
        gc_qdstartime: new Date(now.getTime() + 5 * oneDay + oneHour).toISOString(),
        gc_qdendtime: new Date(now.getTime() + 5 * oneDay + 2 * oneHour).toISOString(),
        gc_startime: new Date(now.getTime() + 5 * oneDay + 3 * oneHour).toISOString(),
        gc_maxplayers: 48,
        gc_minplayers: 12,
        gc_rounds: 4,
        gc_gamesperround: 3,
        gc_allowrepeat: 'allow',
        gc_basescore: 50,
        gc_rules: '快速赛制，适合新手参与',
        signupCount: 8,
        status: GAME_STATUS.UPCOMING
      },
      {
        gc_id: 3,
        gc_name: '周末挑战赛',
        gc_time: new Date(now.getTime() - oneHour).toISOString(),
        gc_bmstartime: new Date(now.getTime() - 2 * oneHour).toISOString(),
        gc_bmendtime: new Date(now.getTime() + oneHour).toISOString(),
        gc_qdstartime: new Date(now.getTime() + oneHour).toISOString(),
        gc_qdendtime: new Date(now.getTime() + 2 * oneHour).toISOString(),
        gc_startime: new Date(now.getTime() + 3 * oneHour).toISOString(),
        gc_maxplayers: 24,
        gc_minplayers: 9,
        gc_rounds: 3,
        gc_gamesperround: 3,
        gc_allowrepeat: 'allow',
        gc_basescore: 0,
        gc_rules: '休闲模式，无积分压力',
        signupCount: 15,
        status: GAME_STATUS.SIGNIN
      },
      {
        gc_id: 4,
        gc_name: '即时对战赛',
        gc_time: new Date(now.getTime() - 2 * oneHour).toISOString(),
        gc_bmstartime: new Date(now.getTime() - 3 * oneHour).toISOString(),
        gc_bmendtime: new Date(now.getTime() - oneHour).toISOString(),
        gc_qdstartime: new Date(now.getTime() - oneHour).toISOString(),
        gc_qdendtime: new Date(now.getTime() - 30 * 60 * 1000).toISOString(), // 30分钟前结束签到
        gc_startime: new Date(now.getTime() + 30 * 60 * 1000).toISOString(), // 30分钟后开始
        gc_maxplayers: 18,
        gc_minplayers: 6,
        gc_rounds: 2,
        gc_gamesperround: 3,
        gc_allowrepeat: 'allow',
        gc_basescore: 20,
        gc_rules: '快速对战，适合体验',
        signupCount: 12,
        status: GAME_STATUS.WAITING
      }
    ]
  }

  // 计算比赛状态
  static calculateGameStatus(config) {
    const now = new Date()
    const signupStart = new Date(config.gc_bmstartime)
    const signupEnd = new Date(config.gc_bmendtime)
    const signinStart = new Date(config.gc_qdstartime)
    const signinEnd = new Date(config.gc_qdendtime)
    const gameStart = new Date(config.gc_startime)

    if (now < signupStart) {
      return GAME_STATUS.UPCOMING
    } else if (now >= signupStart && now < signupEnd) {
      return GAME_STATUS.SIGNUP
    } else if (now >= signinStart && now < signinEnd) {
      return GAME_STATUS.SIGNIN
    } else if (now >= signinEnd && now < gameStart) {
      return GAME_STATUS.WAITING
    } else if (now >= gameStart) {
      // 这里可以根据实际游戏进度判断是PLAYING还是FINISHED
      return GAME_STATUS.PLAYING
    }
    
    return GAME_STATUS.UPCOMING
  }
}

// 报名服务
export class SignupService {
  // 用户报名
  static async signUp(uid, gcid) {
    try {
      const response = await api.signUp(uid, gcid)
      return response
    } catch (error) {
      console.error('报名失败:', error)
      // 模拟报名成功
      return {
        code: 0,
        msg: '报名成功'
      }
    }
  }

  // 取消报名
  static async cancelSignUp(uid, gcid) {
    try {
      const response = await api.cancelSignUp(uid, gcid)
      return response
    } catch (error) {
      console.error('取消报名失败:', error)
      return {
        code: 0,
        msg: '取消报名成功'
      }
    }
  }

  // 用户签到
  static async signIn(uid, gcid) {
    try {
      const response = await api.signIn(uid, gcid)
      return response
    } catch (error) {
      console.error('签到失败:', error)
      return {
        code: 0,
        msg: '签到成功'
      }
    }
  }

  // 获取报名列表
  static async getSignUpList(gcid) {
    try {
      const response = await api.getSignUpList(gcid)
      return response
    } catch (error) {
      console.error('获取报名列表失败:', error)
      return {
        code: 0,
        data: []
      }
    }
  }

  // 检查用户报名状态
  static async checkUserSignupStatus(_uid, gcid) {
    try {
      // 这里应该调用检查用户报名状态的API
      // 暂时返回模拟数据

      // 对于即时对战赛(gcid=4)，模拟用户已报名已签到
      if (gcid === 4) {
        return {
          isSignedUp: true,
          isSignedIn: true,
          signupTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2小时前报名
          signinTime: new Date(Date.now() - 30 * 60 * 1000).toISOString() // 30分钟前签到
        }
      }

      return {
        isSignedUp: false,
        isSignedIn: false,
        signupTime: null,
        signinTime: null
      }
    } catch (error) {
      console.error('检查报名状态失败:', error)
      return {
        isSignedUp: false,
        isSignedIn: false,
        signupTime: null,
        signinTime: null
      }
    }
  }
}
